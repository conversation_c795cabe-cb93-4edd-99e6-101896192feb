/* Feedback Page Styles */
.feedback-page {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
}

.feedback-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.1rem;
  color: var(--gray-70);
}

/* Main Content Layout */
.feedback-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Ratings Section */
.feedback-ratings-section {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.ratings-card,
.feedback-stats-card {
  background-color: var(--card-background-color);
  border: 1px solid var(--gray-30);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Ratings Card */
.ratings-card h3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--gray-100);
}

.ratings-period-selector,
.stats-period-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--gray-70);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
}

.ratings-period-selector svg,
.stats-period-selector svg {
  font-size: 0.75rem;
}

.overall-rating {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
}

.rating-circle {
  text-align: center;
}

.rating-number {
  font-size: 3rem;
  font-weight: 700;
  color: var(--gray-100);
  line-height: 1;
}

.rating-total {
  font-size: 1.5rem;
  color: var(--gray-60);
  margin-left: 0.25rem;
}

.rating-reviews {
  font-size: 0.9rem;
  color: var(--gray-70);
  margin-top: 0.5rem;
}

/* Feedback Statistics Card */
.feedback-stats-card h3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--gray-100);
}

.stats-summary {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stats-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gray-100);
  line-height: 1;
}

.stats-label {
  font-size: 0.85rem;
  color: var(--gray-70);
  margin-top: 0.25rem;
}

/* Monthly Chart */
.monthly-chart {
  display: flex;
  align-items: flex-end;
  gap: 1rem;
  height: 120px;
  padding: 1rem 0;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  height: 100%;
}

.bar-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  height: 80px;
  width: 24px;
  position: relative;
}

.bar {
  width: 100%;
  border-radius: 2px 2px 0 0;
  margin-bottom: 1px;
}

.bar-rating-4-5 {
  background-color: #10b981;
}

.bar-rating-1-3 {
  background-color: #f59e0b;
}

.month-label {
  font-size: 0.75rem;
  color: var(--gray-70);
  margin-top: 0.5rem;
  text-align: center;
}

/* Filters */
.feedback-filters {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.filter-item select {
  padding: 0.5rem 2rem 0.5rem 0.75rem;
  border: 1px solid var(--gray-30);
  border-radius: 8px;
  background-color: var(--gray-10);
  color: var(--gray-100);
  font-size: 0.9rem;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  cursor: pointer;
  min-width: 140px;
}

.filter-item select:focus {
  outline: none;
  border-color: var(--primary-100);
  box-shadow: 0 0 0 3px rgba(242, 108, 249, 0.1);
}

.date-filter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--gray-30);
  border-radius: 8px;
  background-color: var(--gray-10);
  color: var(--gray-100);
  font-size: 0.9rem;
  cursor: pointer;
  margin-left: auto;
}

.date-filter svg {
  font-size: 0.75rem;
  color: var(--gray-70);
}

/* Feedback Cards */
.feedback-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.feedback-card {
  background-color: var(--card-background-color);
  border: 1px solid var(--gray-30);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.2s ease;
}

.feedback-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.feedback-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.feedback-user {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background-color: var(--gray-30);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-60);
  font-size: 1.1rem;
}

.user-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--gray-100);
}

.feedback-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.feedback-rating .star-filled {
  color: #fbbf24;
  font-size: 0.8rem;
}

.feedback-rating .star-empty {
  color: var(--gray-30);
  font-size: 0.8rem;
}

.feedback-rating .rating-number {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--gray-100);
  margin-left: 0.25rem;
}

.feedback-date {
  font-size: 0.85rem;
  color: var(--gray-60);
}

.feedback-content-text {
  color: var(--gray-80);
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.feedback-event-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.event-badge {
  background-color: var(--cool-gray-10);
  border: 1px solid var(--gray-30);
  border-radius: 6px;
  padding: 0.25rem 0.75rem;
}

.event-badge span {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--gray-100);
}

.event-category {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.category-dot {
  width: 8px;
  height: 8px;
  background-color: var(--primary-100);
  border-radius: 50%;
}

.event-category span {
  font-size: 0.8rem;
  color: var(--gray-70);
}

/* Pagination */
.feedback-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--gray-30);
}

.pagination-info {
  color: var(--gray-70);
  font-size: 0.9rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-btn {
  width: 36px;
  height: 36px;
  border: 1px solid var(--gray-30);
  background: var(--gray-10);
  color: var(--gray-70);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
  font-weight: 500;
}

.pagination-btn:hover:not(:disabled) {
  border-color: var(--primary-100);
  color: var(--primary-100);
}

.pagination-btn.active {
  background: var(--primary-100);
  color: var(--gray-10);
  border-color: var(--primary-100);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-ellipsis {
  color: var(--gray-60);
  margin: 0 0.5rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .feedback-ratings-section {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .feedback-cards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .feedback-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-item,
  .date-filter {
    width: 100%;
    margin: 0;
  }

  .feedback-pagination {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .stats-summary {
    justify-content: center;
  }

  .monthly-chart {
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .feedback-card {
    padding: 1rem;
  }

  .feedback-card-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
  }

  .feedback-event-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
